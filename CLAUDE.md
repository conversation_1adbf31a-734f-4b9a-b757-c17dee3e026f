# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

Dream Oracle is a full-stack TypeScript application using:
- **Frontend**: React 19 + Vite + Tailwind CSS 4 + Capacitor (for iOS mobile app)
- **Backend**: Convex real-time database with serverless functions
- **Authentication**: Convex Auth with Resend email OTP
- **AI Integration**: OpenAI GPT-3.5-turbo for dream interpretation and rewriting

The project uses npm workspaces with `convex` and `frontend` packages.

## Development Commands

### Setup
```bash
npm install  # Install all workspace dependencies
```

### Frontend Development
```bash
cd frontend
npm run dev          # Start development server
npm run build        # Production build
npm run lint         # Run ESLint
npm run build:mobile # Build and sync with Capacitor for mobile
npm run dev:ios      # Open iOS project in Xcode
```

### Backend Development
Convex functions are deployed automatically. Use the Convex CLI:
```bash
npx convex dev    # Start Convex development environment
npx convex deploy # Deploy to production
npx convex docs   # Open Convex documentation
```

## Key Architecture Patterns

### Database Schema
- All tables include `userEmail` for user-scoped data access
- Dreams link to transcriptions and interpretations
- Authentication uses tokenIdentifier from Convex Auth

### Frontend Structure
- Native navigation with React Router integration via `capacitor-native-navigation-react-router`
- Convex provider setup in `App.tsx`
- Speech recognition for dream recording
- Modal-based authentication flow

### Backend Functions
- User authentication check in all operations via `getUserByToken()`
- AI processing pipeline: transcription → dream rewriting → interpretation
- OpenAI integration with temperature settings and token limits
- Email OTP authentication via Resend

### Mobile Development
- Capacitor configuration for iOS deployment
- Status bar styling and splash screen setup
- Native navigation integration for mobile-first UX

## Data Flow
1. User records dream via speech recognition
2. Transcription stored in database
3. AI rewrites transcription into coherent dream narrative
4. AI generates dream interpretation
5. All operations are user-scoped and real-time via Convex