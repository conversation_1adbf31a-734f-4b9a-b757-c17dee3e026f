import { convexAuth } from "@convex-dev/auth/server";
import { ResendOTP } from "./ResendOTP";
 
export const { auth, signIn, signOut, store, isAuthenticated } = convexAuth({
  providers: [ResendOTP],
  callbacks: {
    createOrUpdateUser: async (ctx, args) => {
      const email = args.profile.email;
      if (!email) {
        throw new Error("Email is required");
      }

      // If Convex has already linked this identity to a user, return it
      if (args.existingUserId) {
        return args.existingUserId;
      }

      const existingUser = await ctx.db
        .query("users")
        .filter((q) => q.eq(q.field("email"), email))
        .first();

      if (existingUser) {
        return existingUser._id;
      }

      const userId = await ctx.db.insert("users", {
        email,
        tokenIdentifier: `${args.type}:${args.provider.id}:${email}`,
      });

      return userId;
    },
  },
});
