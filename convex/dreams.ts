import { mutation, query, action } from "./_generated/server";
import { v } from "convex/values";
import { OpenAI } from "openai";

import { DREAM_REWRITE_PROMPT } from "./prompts";

import { api } from "./_generated/api";
import { Id } from "./_generated/dataModel";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export const createDream = mutation({
  args: {
    title: v.string(),
    content: v.string(),
    transcriptionId: v.id("transcriptions"),
  },
  handler: async (ctx, args): Promise<Id<"dreams">> => {
    const email: string = await ctx.runQuery(api.users.getUserEmail);
    
    const id = await ctx.db.insert("dreams", {
      title: args.title,
      content: args.content,
      transcriptionId: args.transcriptionId,
      userEmail: email,
    });
    return id;
  },
});

export const createDreamFromTranscription = action({
  args: {
    transcription: v.string(),
  },
  handler: async (ctx, args): Promise<Id<"dreams">> => {
    if (!args.transcription.trim()) {
      throw new Error("Transcription cannot be empty");
    }

    // First, save the transcription
    const transcriptionId = await ctx.runMutation(api.transcriptions.createTranscription, {
      content: args.transcription,
    });

    // Generate dream content from transcription
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo-0125",
      messages: [
        {
          role: "system",
          content: DREAM_REWRITE_PROMPT
        },
        {
          role: "user",
          content: `Here is the transcription:
"""
${args.transcription}
"""`
        }
      ],
      response_format: { type: "json_object" },
    });

    const response = completion.choices[0].message.content;
    if (!response) {
      throw new Error("Failed to generate dream content");
    }

    const { title, content } = JSON.parse(response);
    
    // Create the dream with the transcription reference
    const dreamId = await ctx.runMutation(api.dreams.createDream, {
      title,
      content,
      transcriptionId,
    });

    return dreamId;
  },
});

export const getDream = query({
  args: { id: v.id("dreams") },
  handler: async (ctx, args) => {
    try {
      const email: string = await ctx.runQuery(api.users.getUserEmail);
      
      const dream = await ctx.db.get(args.id);
      
      // Check if the dream exists and belongs to the current user
      if (!dream || dream.userEmail !== email) {
        return null;
      }
      
      return dream;
    } catch (error) {
      // Return null instead of throwing an error for queries
      return null;
    }
  },
});

export const getRecentDreams = query({
  handler: async (ctx) => {
    try {
      const email: string = await ctx.runQuery(api.users.getUserEmail);
      
      return await ctx.db
        .query("dreams")
        .filter((q) => q.eq(q.field("userEmail"), email))
        .order("desc")
        .take(10);
    } catch (error) {
      // Return empty array instead of throwing an error
      return [];
    }
  },
});

export const updateDream = mutation({
  args: {
    id: v.id("dreams"),
    title: v.string(),
    content: v.string(),
  },
  handler: async (ctx, args) => {
    const email: string = await ctx.runQuery(api.users.getUserEmail);
    
    // Get the dream to check ownership
    const dream = await ctx.db.get(args.id);
    
    // Check if the dream exists and belongs to the current user
    if (!dream || dream.userEmail !== email) {
      throw new Error("Dream not found or you don't have permission to update it");
    }
    
    await ctx.db.patch(args.id, {
      title: args.title,
      content: args.content,
    });
  },
});

export const deleteDream = mutation({
  args: {
    id: v.id("dreams"),
  },
  handler: async (ctx, args) => {
    const email: string = await ctx.runQuery(api.users.getUserEmail);
    
    const dream = await ctx.db.get(args.id);
    
    // Check if the dream exists and belongs to the current user
    if (!dream || dream.userEmail !== email) {
      throw new Error("Dream not found or you don't have permission to delete it");
    }
    
    await ctx.db.delete(args.id);
  },
});
