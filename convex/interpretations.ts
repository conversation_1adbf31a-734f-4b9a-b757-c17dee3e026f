import { mutation, query, action } from "./_generated/server";
import { v } from "convex/values";
import { OpenAI } from "openai";

import { api } from "./_generated/api";
import { Id } from "./_generated/dataModel";

import { DREAM_INTERPRETATION_PROMPT } from "./prompts";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const INTERPRETATION_MODEL = "gpt-3.5-turbo-0125";

export const createInterpretation = action({
  args: {
    dreamId: v.id("dreams"),
  },
  handler: async (ctx, args): Promise<Id<"interpretations">> => {
    const email: string = await ctx.runQuery(api.users.getUserEmail);
    
    const dream = await ctx.runQuery(api.dreams.getDream, { id: args.dreamId });
    if (!dream) {
      throw new Error("Dream not found");
    }
    
    // Additional check to ensure the dream belongs to the current user
    if (dream.userEmail !== email) {
      throw new Error("You don't have permission to interpret this dream");
    }

    try {
      const completion = await openai.chat.completions.create({
        model: INTERPRETATION_MODEL,
        messages: [
          {
            role: "system",
            content: DREAM_INTERPRETATION_PROMPT,
          },
          {
            role: "user",
            content: `Title: ${dream.title}\n\nDream: ${dream.content}`,
          },
        ],
      });

      const interpretation = completion.choices[0].message.content;
      if (!interpretation) {
        throw new Error("Failed to generate interpretation");
      }

      const interpretationId = await ctx.runMutation(
        api.interpretations.saveInterpretation,
        {
          dreamId: args.dreamId,
          content: interpretation,
          status: "completed",
          model: INTERPRETATION_MODEL,
        }
      );

      return interpretationId;
    } catch (error) {
      throw new Error("Failed to generate dream interpretation");
    }
  },
});

export const saveInterpretation = mutation({
  args: {
    dreamId: v.id("dreams"),
    content: v.string(),
    status: v.string(),
    model: v.string(),
  },
  handler: async (ctx, args): Promise<Id<"interpretations">> => {
    const email: string = await ctx.runQuery(api.users.getUserEmail);
    
    // Verify the dream belongs to the current user
    const dream = await ctx.db.get(args.dreamId);
    if (!dream || dream.userEmail !== email) {
      throw new Error("Dream not found or you don't have permission to interpret it");
    }
    
    return await ctx.db.insert("interpretations", {
      dreamId: args.dreamId,
      content: args.content,
      status: args.status,
      model: args.model,
      userEmail: email,
    });
  },
});

export const getInterpretation = query({
  args: { dreamId: v.id("dreams") },
  handler: async (ctx, args) => {
    try {
      const email: string = await ctx.runQuery(api.users.getUserEmail);
      
      // First check if the dream belongs to the current user
      const dream = await ctx.db.get(args.dreamId);
      if (!dream || dream.userEmail !== email) {
        return null;
      }
      
      const interpretation = await ctx.db
        .query("interpretations")
        .filter((q) => q.eq(q.field("dreamId"), args.dreamId))
        .filter((q) => q.eq(q.field("userEmail"), email))
        .first();
        
      return interpretation;
    } catch (error) {
      // Return null instead of throwing an error for queries
      return null;
    }
  },
});
