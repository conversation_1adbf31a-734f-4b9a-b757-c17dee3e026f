import { defineSchema, defineTable } from "convex/server";
import { authTables } from "@convex-dev/auth/server";
import { v } from "convex/values";

export default defineSchema({
  ...authTables,
  users: defineTable({
    tokenIdentifier: v.string(),
    // Our custom fields
    email: v.string(),
  }),
  dreams: defineTable({
    title: v.string(),
    content: v.string(),
    transcriptionId: v.id("transcriptions"),
    userEmail: v.string(),
  }),
  transcriptions: defineTable({
    content: v.string(),
    userEmail: v.string(),
  }),
  interpretations: defineTable({
    dreamId: v.id("dreams"),
    content: v.string(),
    status: v.string(),
    model: v.string(),
    userEmail: v.string(),
  }),
});
