import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { api } from "./_generated/api";

export const createTranscription = mutation({
  args: {
    content: v.string(),
  },
  handler: async (ctx, args) => {
    const email: string = await ctx.runQuery(api.users.getUserEmail);
    
    return await ctx.db.insert("transcriptions", {
      content: args.content,
      userEmail: email,
    });
  },
});

export const getTranscription = query({
  args: {
    id: v.id("transcriptions"),
  },
  handler: async (ctx, args) => {
    try {
      const email: string = await ctx.runQuery(api.users.getUserEmail);
      
      const transcription = await ctx.db.get(args.id);
      
      // Check if the transcription exists and belongs to the current user
      if (!transcription || transcription.userEmail !== email) {
        return null;
      }
      
      return transcription;
    } catch (error) {
      // Return null instead of throwing an error for queries
      return null;
    }
  },
});
