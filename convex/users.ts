import { query } from "./_generated/server";
import { Id } from "./_generated/dataModel";

/**
 * Get the current user's email from their authentication identity
 */
export const getUserEmail = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    
    // The identity.subject is the user ID in Convex
    const userId = identity.subject.split('|')[0] as Id<"users">;
    console.log(`user ID: ${userId}`);
    
    // Get the user from the database
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error("User not found");
    }
    console.log(`user: ${JSON.stringify(user)}`);
    
    return user.email;
  },
});