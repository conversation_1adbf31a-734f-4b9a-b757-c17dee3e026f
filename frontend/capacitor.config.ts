import type { CapacitorConfig } from "@capacitor/cli";

const config: CapacitorConfig = {
  appId: "com.refactornator.dreamoracle",
  appName: "Dream Oracle",
  webDir: "dist",
  backgroundColor: "#14082C",
  ios: {
    backgroundColor: "#14082C",
  },
  android: {
    backgroundColor: "#14082C",
  },
  plugins: {
    SentryCapacitor: {
      dsn: process.env.VITE_SENTRY_DSN,
      debug: process.env.NODE_ENV === "development",
      environment: process.env.NODE_ENV || "development",
    },
  },
};

export default config;
