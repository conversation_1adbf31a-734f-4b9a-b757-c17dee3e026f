<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dream Oracle</title>
    <style>
      /* Reset base elements */
      body {
        color-scheme: dark;
        background-color: #14082c; /* ELIMINATES THE WHITE FLASH WHEN A NEW WEBVIEW LOADS */
      }

      html,
      body,
      #root {
        width: 100%;
        height: 100vh;
        margin: 0;
        padding: 0;
      }
    </style>
    <script>
      if (window.Capacitor) {
        document.documentElement.classList.add("capacitor");
      }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
