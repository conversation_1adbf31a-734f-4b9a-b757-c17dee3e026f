{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "build:mobile": "npm run build && npx cap sync", "dev:ios": "npx cap open ios"}, "dependencies": {"@capacitor/assets": "^3.0.5", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/ios": "^7.2.0", "@capacitor/status-bar": "^7.0.1", "@fontsource/crimson-pro": "^5.2.6", "@fontsource/inter": "^5.2.6", "@sentry/capacitor": "^2.1.0", "@sentry/react": "^9.38.0", "@tailwindcss/vite": "^4.1.4", "capacitor-native-navigation": "^0.10.2", "capacitor-native-navigation-react": "^6.3.4", "capacitor-native-navigation-react-router": "^7.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.9.2", "lucide-react": "^0.503.0", "posthog-js": "^1.240.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.2", "react-speech-recognition": "^4.0.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@sentry/vite-plugin": "^4.0.2", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-speech-recognition": "^3.9.6", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}