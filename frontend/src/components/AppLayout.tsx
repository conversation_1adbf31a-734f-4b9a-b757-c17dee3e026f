import React from "react";

interface AppLayoutProps {
  children: React.ReactNode;
}

export function AppLayout({ children }: AppLayoutProps): React.ReactElement {
  return (
    <>
      {/* Fixed gradient background layer */}
      <div className="fixed w-full min-h-screen inset-0 bg-gradient-to-b from-[#14082C] to-[#1C0A40] -z-10" />

      {/* Content container */}
      <div
        className="relative w-full min-h-screen flex flex-col"
        style={{
          paddingTop: "calc(env(safe-area-inset-top, 0px) + 90px)",
        }}
      >
        {children}
      </div>
    </>
  );
}
