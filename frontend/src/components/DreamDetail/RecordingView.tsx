import { useEffect, useState } from "react";
import { Mic, StopCircle } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';
import { cn } from "@/lib/utils";

import { Button } from "@/components/ui/button";
import { trackDreamRecordStarted, trackDreamRecordStopped } from "@/lib/utils";

interface RecordingViewProps {
  onStopRecording: (transcription: string) => void;
}

const contentAnimation = {
  initial: { opacity: 0, y: 10 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.4, ease: "easeOut", staggerChildren: 0.15 }
};

export const RecordingView = ({ onStopRecording }: RecordingViewProps) => {
  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition
  } = useSpeechRecognition();
  const [isStoppingRecording, setIsStoppingRecording] = useState(false);

  useEffect(() => {
    if (!browserSupportsSpeechRecognition) {
      console.error('Speech recognition not supported');
      return;
    }

    SpeechRecognition.startListening({ continuous: true });
    trackDreamRecordStarted();

    return () => {
      SpeechRecognition.stopListening();
      trackDreamRecordStopped();
    };
  }, [browserSupportsSpeechRecognition]);

  const handleStop = () => {
    if (!transcript.trim()) return; // Prevent stopping if transcript is empty
    
    setIsStoppingRecording(true);
    SpeechRecognition.stopListening();

    // Wait for final transcription to be processed
    setTimeout(() => {
      const finalTranscript = transcript.trim();
      if (finalTranscript) {
        onStopRecording(finalTranscript);
        resetTranscript();
      }
      setIsStoppingRecording(false);
    }, 300);
  };

  if (!browserSupportsSpeechRecognition) {
    return (
      <div className="flex items-center justify-center">
        <div className="text-center text-white/60">
          Your browser doesn't support speech recognition.
        </div>
      </div>
    );
  }

  const hasContent = transcript.trim().split(/\s+/).length >= 3;

  return (
    <motion.div 
      className="flex flex-col items-center min-h-[80vh] py-8"
      variants={contentAnimation}
      initial="initial"
      animate="animate"
    >
      <motion.div variants={contentAnimation} className="text-center mb-24">
        <h2 className="font-crimson text-4xl text-white tracking-tighter">
          Tell us your dream...
          <br />
          The Oracle is listening
        </h2>
      </motion.div>

      <motion.div 
        variants={contentAnimation}
        className={cn(
          "text-white transition-opacity duration-300 mb-24",
          listening && "animate-pulse"
        )}
      >
        <Mic className="w-42 h-42" />
      </motion.div>

      <AnimatePresence>
        {hasContent && (
          <motion.div 
            className="w-full max-w-2xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.3 }}
          >
            <Button
              variant="danger"
              size="lg"
              className="w-full"
              onClick={handleStop}
              disabled={isStoppingRecording}
            >
              <StopCircle className="w-5 h-5" />
              {isStoppingRecording ? 'Finishing up...' : 'Stop Recording'}
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};
