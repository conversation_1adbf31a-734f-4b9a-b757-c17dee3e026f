import { useRef, useEffect, useState } from "react";
import { useQuery, useAction } from "convex/react";
import { motion } from "framer-motion";
import { Spark<PERSON> } from "lucide-react";
import { Button } from "@/components/ui/button";
import { api } from "../../../../convex/_generated/api";
import { Id } from "../../../../convex/_generated/dataModel";
import { trackInterpretationRequested, trackInterpretationViewed } from "@/lib/utils";

interface ReviewViewProps {
  dream: {
    id: Id<"dreams">;
    title: string;
    content: string;
  };
  onUpdateTitle: (value: string) => void;
  onUpdateContent: (value: string) => void;
}

const contentAnimation = {
  initial: { opacity: 0, y: 10 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.4, ease: "easeOut", staggerChildren: 0.1 }
};

export const ReviewView = ({
  dream,
  onUpdateTitle,
  onUpdateContent,
}: ReviewViewProps) => {
  const [isInterpreting, setIsInterpreting] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);

  const interpretation = useQuery(api.interpretations.getInterpretation, {
    dreamId: dream.id,
  });
  useEffect(() => {
    if (interpretation) {
      trackInterpretationViewed();
    }
  }, [interpretation]);
  const createInterpretation = useAction(
    api.interpretations.createInterpretation
  );

  useEffect(() => {
    if (
      contentRef.current &&
      contentRef.current.textContent !== dream.content
    ) {
      contentRef.current.textContent = dream.content;
    }
    if (titleRef.current && titleRef.current.textContent !== dream.title) {
      titleRef.current.textContent = dream.title;
    }
  }, [dream.content, dream.title]);

  const handleTitleInput = (e: React.FormEvent<HTMLHeadingElement>) => {
    onUpdateTitle(e.currentTarget.textContent || "");
  };

  const handleContentInput = (e: React.FormEvent<HTMLDivElement>) => {
    onUpdateContent(e.currentTarget.textContent || "");
  };

  const handleInterpret = async () => {
    setIsInterpreting(true);
    trackInterpretationRequested();
    try {
      await createInterpretation({
        dreamId: dream.id,
      });
    } finally {
      setIsInterpreting(false);
    }
  };

  const renderInterpretationSection = () => {
    if (interpretation) {
      return (
        <div className="w-full space-y-4 px-2">
          <h2 className="font-inter font-semibold text-2xl text-white/90">
            What does it mean...
          </h2>
          <p className="font-inter text-white/90 text-lg leading-relaxed">
            {interpretation.content}
          </p>
        </div>
      );
    }

    if (isInterpreting) {
      return (
        <div className="w-full py-12 flex flex-col items-center justify-center space-y-6">
          <div className="w-32 h-32 rounded-full bg-[#6D28D9]/20 animate-pulse flex items-center justify-center">
            <Sparkles className="w-24 h-24 text-[#C7B2FF]" />
          </div>
          <div className="text-center">
            <p className="text-xl text-white/90 font-crimson">
              Consulting the Oracle...
            </p>
          </div>
        </div>
      );
    }

    return (
      <Button
        variant="primary"
        size="lg"
        className="w-full"
        onClick={handleInterpret}
      >
        <Sparkles className="w-5 h-5" />
        Interpret This Dream
      </Button>
    );
  };

  return (
    <motion.div 
      className="flex flex-col items-center space-y-8 max-w-2xl mx-auto"
      variants={contentAnimation}
      initial="initial"
      animate="animate"
    >
      <motion.h1
        ref={titleRef}
        variants={contentAnimation}
        className="font-crimson text-4xl text-white text-center outline-none empty:before:content-[attr(data-placeholder)] empty:before:text-white/50 px-4"
        contentEditable
        onInput={handleTitleInput}
        suppressContentEditableWarning
        data-placeholder="Enter dream title..."
      />

      <motion.div
        ref={contentRef}
        variants={contentAnimation}
        className="w-full bg-[#FAF3ED] rounded-2xl px-6 py-4 min-h-[200px] max-h-[400px] overflow-x-hidden overflow-y-auto outline-none empty:before:content-[attr(data-placeholder)] empty:before:text-[#2D2D2D]/50 font-crimson font-light text-2xl leading-relaxed text-[#000000]"
        contentEditable
        onInput={handleContentInput}
        suppressContentEditableWarning
        data-placeholder="Describe your dream..."
      />

      <motion.div variants={contentAnimation}>
        {renderInterpretationSection()}
      </motion.div>
    </motion.div>
  );
};
