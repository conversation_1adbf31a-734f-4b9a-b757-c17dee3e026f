import React, { Component, ErrorInfo, ReactNode } from "react";
import * as Sentry from "@sentry/capacitor";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("ErrorBoundary caught an error:", error, errorInfo);

    // Report error to Sentry
    Sentry.withScope((scope) => {
      scope.setTag("errorBoundary", true);
      scope.setContext("errorInfo", {
        componentStack: errorInfo.componentStack,
      });
      Sentry.captureException(error);
    });
  }

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return (
        this.props.fallback || (
          <div className="min-h-screen bg-gradient-to-b from-[#14082C] to-[#1C0A40] flex items-center justify-center p-6">
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-6 max-w-md w-full text-center">
              <h2 className="text-xl font-semibold text-red-400 mb-4">
                Something went wrong
              </h2>
              <p className="text-red-300 mb-4">
                An unexpected error occurred. The error has been reported and we're working to fix it.
              </p>
              <button
                onClick={() => window.location.reload()}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors"
              >
                Reload App
              </button>
              {this.state.error && (
                <details className="mt-4 text-left">
                  <summary className="text-red-400 cursor-pointer">
                    Error Details
                  </summary>
                  <pre className="text-xs text-red-300 mt-2 overflow-auto">
                    {this.state.error.message}
                  </pre>
                </details>
              )}
            </div>
          </div>
        )
      );
    }

    return this.props.children;
  }
}

// Higher-order component for easier usage
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ErrorBoundary>
  );
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// Sentry's built-in error boundary (alternative approach)
// Note: Import from @sentry/react if you need the built-in ErrorBoundary
// import { ErrorBoundary as SentryErrorBoundary } from "@sentry/react";
// export { SentryErrorBoundary };
