import { useEffect } from 'react';
import { Capacitor } from '@capacitor/core';
import { StatusBar, Style } from '@capacitor/status-bar';

export function StatusBarConfig() {
  useEffect(() => {
    const configureStatusBar = async () => {
      if (Capacitor.isNativePlatform()) {
        try {
          await StatusBar.setStyle({ style: Style.Dark });
          await StatusBar.setBackgroundColor({ color: '#C7B2FF' });
        } catch (error) {
          console.error('Error configuring status bar:', error);
        }
      }
    };

    configureStatusBar();
  }, []);

  return null;
}
