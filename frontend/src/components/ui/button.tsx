import { motion, HTMLMotionProps } from "framer-motion";
import { cn } from "@/lib/utils";

interface ButtonProps extends Omit<HTMLMotionProps<"button">, "children"> {
  variant?: "default" | "primary" | "danger";
  size?: "default" | "lg" | "icon";
  children: React.ReactNode;
}

const Button = ({
  variant = "default",
  size = "default",
  className,
  children,
  ...props
}: ButtonProps) => {
  const baseStyles = cn(
    "relative font-inter font-medium rounded-full",
    "select-none touch-none", // Prevent text selection and improve touch handling
    "active:opacity-90", // Better feedback on mobile touch
    "disabled:opacity-50 disabled:pointer-events-none",
  );

  const variantStyles = {
    default: "bg-white/10 text-[#F5E9E0] shadow-sm",
    primary:
      "bg-gradient-to-r from-[#3D399A] to-[#975BB8] text-[#F5E9E0] shadow-lg",
    danger: "bg-[#E84949] text-[#F5E9E0] shadow-lg",
  };

  const sizeStyles = {
    default: "h-12 px-6 text-base", // Increased from h-11 for better touch target
    lg: "h-14 px-8 text-lg",
    icon: "h-12 w-12 p-0",
  };

  return (
    <motion.button
      className={cn(
        baseStyles,
        variantStyles[variant],
        sizeStyles[size],
        className,
      )}
      whileTap={{ scale: 0.96 }}
      transition={{
        type: "tween",
        ease: "easeInOut",
      }}
      {...props}
    >
      <div className="flex items-center justify-center gap-2">{children}</div>
    </motion.button>
  );
};

export { Button };
