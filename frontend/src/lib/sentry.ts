import * as Sentry from '@sentry/capacitor';
import * as SentryReact from '@sentry/react';
import { Capacitor } from "@capacitor/core";
import { getConvexUrl } from "./convex";

/**
 * Generate trace propagation targets for Convex backend
 *
 * Convex uses WebSocket connections and HTTP endpoints that need proper
 * trace propagation configuration for performance monitoring.
 */
function getTracePropagationTargets(): (string | RegExp)[] {
  const convexUrl = getConvexUrl();
  const targets: (string | RegExp)[] = [
    // Always include localhost for development
    "localhost",
    /^https?:\/\/localhost(:\d+)?/,
  ];

  if (convexUrl) {
    try {
      const url = new URL(convexUrl);
      const convexDomain = url.hostname;

      // Add the specific Convex deployment domain
      targets.push(convexUrl);

      // Add the base Convex domain (e.g., *.convex.cloud)
      if (convexDomain.includes('.convex.cloud')) {
        targets.push(/^https:\/\/.*\.convex\.cloud/);
      }

      // Add WebSocket endpoints (Convex uses both HTTP and WebSocket)
      targets.push(convexUrl.replace('https://', 'wss://'));

      console.log("🔗 Configured trace propagation for Convex:", convexDomain);
    } catch {
      console.warn("⚠️ Invalid Convex URL format:", convexUrl);
      // Fallback: add common Convex patterns
      targets.push(/^https:\/\/.*\.convex\.cloud/);
    }
  } else {
    // Fallback for when Convex URL is not configured
    console.warn("⚠️ Convex URL not configured, using fallback trace targets");
    targets.push(/^https:\/\/.*\.convex\.cloud/);
  }

  return targets;
}

/**
 * Initialize Sentry error monitoring for Dream Oracle
 *
 * This function sets up comprehensive error monitoring including:
 * - Error capture and reporting
 * - Performance monitoring with Convex backend tracing
 * - Session replay
 * - Platform-specific configuration
 * - Environment-specific sampling rates
 */
export function initializeSentry(): void {
  // Get Sentry DSN from environment variables with fallback
  const sentryDsn = import.meta.env.VITE_SENTRY_DSN || 
    "https://<EMAIL>/4509777999626240";

  const environment = import.meta.env.MODE || "development";
  const isProduction = environment === "production";

  try {
    Sentry.init({
      dsn: sentryDsn,
      environment,
      integrations: [
        Sentry.browserTracingIntegration(),
        Sentry.replayIntegration({
          maskAllText: false,
          blockAllMedia: false,
        })
      ],
      // Configure trace propagation for Convex backend
      tracePropagationTargets: getTracePropagationTargets(),
      
      // Performance Monitoring - more conservative in production
      tracesSampleRate: isProduction ? 0.1 : 1.0,
      
      // Session Replay - capture more in development for debugging
      replaysSessionSampleRate: isProduction ? 0.1 : 1.0,
      replaysOnErrorSampleRate: 1.0, // Always capture sessions with errors
      
      // Enhanced error filtering and context
      beforeSend(event) {
        // Add platform information for better debugging
        const platformInfo = getPlatformInfo();
        event.tags = {
          ...event.tags,
          ...platformInfo.tags,
        };

        // Add device context for native platforms
        if (platformInfo.isNative) {
          event.contexts = {
            ...event.contexts,
            device: {
              ...event.contexts?.device,
              model: platformInfo.platform,
            },
          };
        }

        return event;
      },
      
      // Filter out noisy transactions in development
      beforeSendTransaction(event) {
        if (!isProduction) {
          // Filter out hot-reload related transactions
          if (event.transaction?.includes("hot-update") || 
              event.transaction?.includes("__vite")) {
            return null;
          }
        }
        return event;
      },
    },
    // Forward the init method from @sentry/react for proper React integration
    SentryReact.init);

    // Set application context
    setApplicationContext();

    console.log("✅ Sentry initialized successfully");
  } catch (error) {
    console.error("❌ Failed to initialize Sentry:", error);
  }
}

/**
 * Get platform-specific information for error tagging
 */
function getPlatformInfo() {
  const isNative = Capacitor.isNativePlatform();
  const platform = isNative ? Capacitor.getPlatform() : "web";

  return {
    isNative,
    platform,
    tags: {
      platform,
      isNative,
    },
  };
}

/**
 * Set application-specific context in Sentry
 */
function setApplicationContext(): void {
  const platformInfo = getPlatformInfo();
  
  Sentry.setContext("app", {
    name: "Dream Oracle",
    version: "1.0.0",
    platform: platformInfo.platform,
    environment: import.meta.env.MODE || "development",
  });

  // Set additional tags for filtering
  Sentry.setTag("app.name", "dream-oracle");
  Sentry.setTag("app.platform", platformInfo.platform);
}

/**
 * Capture a test error to verify Sentry is working
 * Only available in development mode
 */
export function captureTestError(): void {
  if (import.meta.env.MODE === "production") {
    console.warn("Test errors are disabled in production");
    return;
  }

  Sentry.captureException(new Error("Test error from Dream Oracle - Sentry integration verified!"));
  console.log("🧪 Test error sent to Sentry");
}

/**
 * Add a breadcrumb for debugging
 */
export function addBreadcrumb(message: string, category: string = "info", level: "info" | "warning" | "error" = "info"): void {
  Sentry.addBreadcrumb({
    message,
    category,
    level,
    timestamp: Date.now() / 1000,
  });
}

/**
 * Set user context for error tracking
 */
export function setUserContext(userId: string, email?: string): void {
  Sentry.setUser({
    id: userId,
    email,
  });
}

/**
 * Clear user context (e.g., on logout)
 */
export function clearUserContext(): void {
  Sentry.setUser(null);
}

/**
 * Get trace propagation configuration status for debugging
 *
 * @returns {object} Trace propagation configuration information
 */
export function getTracePropagationStatus() {
  const targets = getTracePropagationTargets();
  const convexUrl = getConvexUrl();

  return {
    convexUrl: convexUrl || "Not configured",
    tracePropagationTargets: targets,
    environment: import.meta.env.MODE || "development",
    isConvexConfigured: Boolean(convexUrl),
  };
}
