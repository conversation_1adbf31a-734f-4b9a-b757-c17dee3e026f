import posthog from "posthog-js";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export type PostHogProperties = {
  page?: string;
  platform?: string;
  signed_in?: boolean;
  app_version?: string;
  dream_count?: number;
};

// Initialize PostHog
posthog.init(import.meta.env.VITE_POSTHOG_API_KEY, {
  api_host: "https://app.posthog.com",
});

// Helper function to capture events
export function captureEvent(
  eventName: string,
  properties: PostHogProperties = {},
) {
  const defaultProperties = {
    platform: "web", // or mobile
    signed_in: !!posthog.get_distinct_id(),
    app_version: "1.0.0", // Update this version as appropriate
    dream_count: 0, // Placeholder, replace with actual user dream count if available
  };
  posthog.capture(eventName, { ...defaultProperties, ...properties });
}

// Functions for the specific events
export function trackUserSignedUp() {
  captureEvent("user_signed_up");
}

export function trackUserLoggedIn() {
  captureEvent("user_logged_in");
}

export function trackUserSignedOut() {
  captureEvent("user_signed_out");
}

export function trackDreamRecordStarted() {
  captureEvent("dream_record_started");
}

export function trackDreamRecordStopped() {
  captureEvent("dream_record_stopped");
}

export function trackDreamTranscriptionReceived() {
  captureEvent("dream_transcription_received");
}

export function trackDreamSaved() {
  captureEvent("dream_saved");
}

export function trackDreamDeleted() {
  captureEvent("dream_deleted");
}

export function trackInterpretationRequested() {
  captureEvent("interpretation_requested");
}

export function trackInterpretationViewed() {
  captureEvent("interpretation_viewed");
}

export function trackPageViewed(page: string) {
  captureEvent("page_viewed", { page });
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(date: number): string {
  return new Date(date).toLocaleDateString("en-US", {
    month: "long",
    day: "numeric",
  });
}
