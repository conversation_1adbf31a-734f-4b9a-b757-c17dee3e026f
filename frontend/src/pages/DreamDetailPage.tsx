import { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useQuery, useMutation, useAction } from "convex/react";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useNativeNavigationViewContext } from "capacitor-native-navigation-react";
import {
  isNativeNavigationAvailable,
  NativeNavigation,
} from "capacitor-native-navigation";
import { goToDream } from "@/utils/navigation";
import { trackDreamSaved, trackDreamTranscriptionReceived } from "@/lib/utils";
import { addBreadcrumb } from "@/lib/sentry";

import { RecordingView } from "@/components/DreamDetail/RecordingView";
import { ReviewView } from "@/components/DreamDetail/ReviewView";
import { AppLayout } from "@/components/AppLayout";
import { Button } from "@/components/ui/button";

import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";

const fadeAnimation = {
  initial: { opacity: 0, y: 10 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: 10 },
  transition: { duration: 1.0, ease: "easeOut" as const },
};

const LoadingSpinner = () => (
  <div className="w-full h-full flex items-center justify-center">
    <div className="w-12 h-12 rounded-full bg-white/10 animate-pulse flex items-center justify-center">
      <Sparkles className="w-8 h-8 text-white/60" />
    </div>
  </div>
);

export function DreamDetailPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { updateView, addClickListener } = useNativeNavigationViewContext();
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Track page navigation
  useEffect(() => {
    if (id === "new") {
      addBreadcrumb("User navigated to new dream recording page", "navigation", "info");
    } else {
      addBreadcrumb(`User navigated to dream detail page: ${id}`, "navigation", "info");
    }
  }, [id]);
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [dirty, setDirty] = useState(false);

  const isNewDream = id === "new";

  const dream = useQuery(
    api.dreams.getDream,
    id && id !== "new" ? { id: id as unknown as Id<"dreams"> } : "skip",
  );
  const updateDream = useMutation(api.dreams.updateDream);
  const createDreamFromTranscription = useAction(
    api.dreams.createDreamFromTranscription,
  );
  const deleteDream = useMutation(api.dreams.deleteDream);

  const handleSave = useCallback(async () => {
    if (!id || id === "new") return;

    addBreadcrumb(`User initiated save for dream: ${id}`, "user-action", "info");

    try {
      await updateDream({
        id: id as Id<"dreams">,
        title,
        content,
      });
      trackDreamSaved();
      setDirty(false);
      addBreadcrumb(`Dream saved successfully: ${id}`, "api", "info");
    } catch (error) {
      addBreadcrumb(`Failed to save dream: ${id}`, "api", "error");
      console.error("Failed to save dream:", error);
    }
  }, [id, updateDream, title, content]);

  // Set up the save button in the navigation bar
  useEffect(() => {
    if (!isNewDream && !isRecording) {
      updateView({
        stackItem: {
          rightItems: [
            {
              id: "save",
              title: dirty ? "Save" : "",
            },
          ],
        },
      });

      return addClickListener(({ buttonId }) => {
        if (buttonId === "save" && dirty) {
          handleSave();
        }
      });
    }
  }, [
    updateView,
    addClickListener,
    dirty,
    isNewDream,
    isRecording,
    handleSave,
  ]);

  useEffect(() => {
    if (dream) {
      setTitle(dream.title);
      setContent(dream.content);

      // 🐛 MOCK BUG FOR SENTRY TUTORIAL - REMOVE AFTER RECORDING
      // Realistic React mistake: Assuming API response structure without proper type checking
      // This simulates a common error where developers assume backend data structure
      if (!isNewDream) {
        // Cast to any to simulate how this error would slip through in real development
        // (Developer assumes the API returns additional metadata fields)
        const dreamData = dream as any;

        // Attempting to access nested properties that don't exist
        // This looks like legitimate code for extracting analytics data
        const analysisData = dreamData.analysis?.sentiment?.score || 0;
        const dreamTags = dreamData.metadata?.tags?.filter((tag: string) => tag.length > 0) || [];

        // The 'analysis' property doesn't exist, so dreamData.analysis is undefined
        // Accessing .sentiment on undefined will throw a TypeError
        const sentimentLevel = dreamData.analysis.sentiment.level; // TypeError: Cannot read properties of undefined

        // This would normally be used for dream categorization and analytics
        console.log(`Sentiment: ${sentimentLevel}, Score: ${analysisData}, Tags: ${dreamTags.length}`);
      }
      // 🐛 END MOCK BUG
    }
  }, [dream, isNewDream]);

  useEffect(() => {
    if (isNewDream) {
      setIsRecording(true);
    }
  }, [isNewDream]);

  const handleStopRecording = async (transcription: string) => {
    setIsRecording(false);
    setIsProcessing(true);
    trackDreamTranscriptionReceived();
    try {
      const dreamId = await createDreamFromTranscription({
        transcription,
      });
      trackDreamSaved();
      // Replace the current route with the new dream
      goToDream(dreamId, navigate, { replace: true });
    } catch (error) {
      console.error("Failed to create dream:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleForgetDream = useCallback(async () => {
    if (!id || id === "new") return;

    addBreadcrumb(`User initiated delete for dream: ${id}`, "user-action", "info");

    if (!confirm("Forget this dream? This cannot be undone.")) {
      addBreadcrumb("User cancelled dream deletion", "user-action", "info");
      return;
    }

    try {
      await deleteDream({ id: id as Id<"dreams"> });
      addBreadcrumb(`Dream deleted successfully: ${id}`, "api", "info");

      if (isNativeNavigationAvailable()) {
        await NativeNavigation.pop({ animated: true });
      } else {
        navigate("/", { replace: true });
      }
      addBreadcrumb("User navigated back after dream deletion", "navigation", "info");
    } catch (error) {
      addBreadcrumb(`Failed to delete dream: ${id}`, "api", "error");
      console.error("Failed to delete dream:", error);
    }
  }, [id, deleteDream, navigate]);

  const handleUpdateTitle = (newTitle: string) => {
    setTitle(newTitle);
    setDirty(true);
  };

  const handleUpdateContent = (newContent: string) => {
    setContent(newContent);
    setDirty(true);
  };

  return (
    <AppLayout>
      <div className="px-4 pt-6 pb-8 flex flex-col flex-1">
        <AnimatePresence mode="wait">
          {isNewDream ? (
            isRecording ? (
              <motion.div key="recording" {...fadeAnimation}>
                <RecordingView onStopRecording={handleStopRecording} />
              </motion.div>
            ) : isProcessing ? (
              <motion.div key="processing" {...fadeAnimation}>
                <div className="flex flex-col items-center justify-center space-y-6">
                  <div className="w-32 h-32 rounded-full bg-[#6D28D9]/20 animate-pulse flex items-center justify-center">
                    <Sparkles className="w-24 h-24 text-[#C7B2FF]" />
                  </div>
                  <p className="text-xl text-white/90 text-center font-crimson">
                    Storing your dream in the astral plane...
                    <br />
                    <span className="text-sm text-white/70 mt-2 block">
                      The Oracle is processing your vision
                    </span>
                  </p>
                </div>
              </motion.div>
            ) : (
              <motion.div key="error" {...fadeAnimation}>
                <div className="text-center text-white/70">
                  Something went wrong...
                </div>
              </motion.div>
            )
          ) : dream ? (
            <motion.div
              key="review"
              {...fadeAnimation}
              className="flex flex-col flex-1 justify-between h-full"
            >
              <ReviewView
                dream={{
                  id: dream._id,
                  title,
                  content,
                }}
                onUpdateTitle={handleUpdateTitle}
                onUpdateContent={handleUpdateContent}
              />
              <Button
                variant="danger"
                className="self-end mt-6"
                size="icon"
                onClick={handleForgetDream}
              >
                <X className="w-5 h-5" />
              </Button>
            </motion.div>
          ) : (
            <motion.div key="loading" {...fadeAnimation}>
              <LoadingSpinner />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </AppLayout>
  );
}
