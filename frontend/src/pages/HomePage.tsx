import { useEffect } from "react";
import { Mic } from "lucide-react";
import { useQuery } from "convex/react";
import { useNavigate, useLocation } from "react-router-dom";
import { useNativeNavigationViewContext } from "capacitor-native-navigation-react";
import { motion, AnimatePresence } from "framer-motion";

import { addBreadcrumb } from "@/lib/sentry";
import { useAuthActions, useAuthToken } from "@convex-dev/auth/react";

import { Button } from "@/components/ui/button";
import { formatDate } from "@/lib/utils";
import { AppLayout } from "@/components/AppLayout";
import { goToDream } from "@/utils/navigation";

import { api } from "../../../convex/_generated/api";
import { trackUserSignedOut, trackPageViewed } from "@/lib/utils";

export function HomePage() {
  const navigate = useNavigate();
  const dreams = useQuery(api.dreams.getRecentDreams);
  const token = useAuthToken();
  const isAuthenticated = !!token;
  const { signOut } = useAuthActions();
  const location = useLocation();

  useEffect(() => {
    addBreadcrumb("HomePage component mounted", "navigation", "info");
    trackPageViewed(location.pathname);
  }, [location.pathname]);

  // Track API query results
  useEffect(() => {
    if (dreams !== undefined) {
      addBreadcrumb(`Dreams loaded: ${dreams.length} dreams found`, "api", "info");
    }
  }, [dreams]);

  const { updateView, addClickListener } = useNativeNavigationViewContext();

  // Add sign out button to navigation bar
  useEffect(() => {
    if (isAuthenticated) {
      updateView({
        stackItem: {
          rightItems: [
            {
              id: "signout",
              title: "Sign Out",
            },
          ],
        },
      });

      return addClickListener(({ buttonId }) => {
        if (buttonId === "signout") {
          addBreadcrumb("User clicked sign out button", "user-action", "info");
          trackUserSignedOut();
          try {
            signOut();
            addBreadcrumb("User signed out successfully", "auth", "info");
          } catch (error) {
            addBreadcrumb("Sign out failed", "auth", "error");
            console.error("Sign out failed:", error);
          }
        }
      });
    } else {
      updateView({
        stackItem: {
          rightItems: [],
        },
      });
    }
  }, [isAuthenticated, updateView, addClickListener, signOut]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        when: "beforeChildren",
        staggerChildren: 0.2
      }
    }
  };
  
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.5,
        ease: "easeOut" as const
      }
    }
  };

  return (
    <AppLayout>
      <main className="flex flex-col items-center px-6">
        <div className="w-full max-w-md flex flex-col items-center">
          {/* Logo and Title Section */}
          <div className="flex flex-col items-center gap-2 mb-12 pt-4">
            <img
              src="/dream-oracle-logo.png"
              alt="Dream Oracle Logo"
              className="w-32 h-32"
            />
            <h1 className="font-crimson font-semibold text-6xl text-white text-center tracking-wider leading-[90%]">
              DREAM
              <br />
              ORACLE
            </h1>
          </div>

          <AnimatePresence>
            {isAuthenticated ? (
              <motion.div
                className="w-full"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                exit={{ opacity: 0 }}
                key="authenticated-content"
              >
                {/* Recording Button */}
                <motion.div variants={itemVariants} className="mb-16">
                  <Button
                    variant="primary"
                    size="lg"
                    className="w-full"
                    onClick={() => {
                      addBreadcrumb("User clicked 'Start Recording a Dream' button", "user-action", "info");
                      goToDream("new", navigate);
                    }}
                  >
                    <Mic className="w-6 h-6" />
                    Start Recording a Dream
                  </Button>
                </motion.div>

                {/* Recent Dreams Section */}
                {dreams && dreams.length > 0 && (
                  <motion.div variants={itemVariants} className="w-full">
                    <h2 className="text-2xl font-semibold text-white/90 mb-6">
                      Recent Dreams
                    </h2>
                    <div className="space-y-6">
                      {dreams.map((dream) => (
                        <motion.div
                          key={dream._id}
                          className="border-b border-white/10 pb-4 cursor-pointer hover:bg-white/5 rounded-lg transition-colors px-4 -mx-4"
                          onClick={() => {
                            addBreadcrumb(`User clicked on dream: ${dream.title}`, "user-action", "info");
                            goToDream(dream._id, navigate);
                          }}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <h3 className="text-xl mb-1 text-white font-medium">
                            {dream.title}
                          </h3>
                          <p className="text-sm text-[#C7B2FF]">
                            {formatDate(dream._creationTime)}
                          </p>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                )}
              </motion.div>
            ) : null}
          </AnimatePresence>
        </div>
      </main>
    </AppLayout>
  );
}
