import { useEffect } from "react";
import { <PERSON><PERSON>er<PERSON>outer, Route, Routes } from "react-router-dom";
import {
  isNativeNavigationAvailable,
  NativeNavigation,
} from "capacitor-native-navigation";
import {
  initReact,
  NativeNavigationProvider,
} from "capacitor-native-navigation-react";
import {
  NativeNavigationNavigatorOptions,
  NativeNavigationRouter,
} from "capacitor-native-navigation-react-router";
import { addBreadcrumb } from "@/lib/sentry";
import { useAuthToken } from "@convex-dev/auth/react";

import { HomePage } from "@/pages/HomePage";
import { DreamDetailPage } from "@/pages/DreamDetailPage";
import { defaultStackItemConfig } from "@/utils/navigation";
import { SignInModalContainer } from "./components/SignInModal";

const nativeNavigationReact = initReact({
  plugin: NativeNavigation,
});

const nativeNavigationNavigatorOptions: NativeNavigationNavigatorOptions = {
  modals: [],
  errorHandler: (source: string, error: unknown) => {
    console.error(`Navigation error from ${source}:`, error);
  },
};

async function setupRootStack() {
  if (!isNativeNavigationAvailable()) return;

  addBreadcrumb("Setting up native navigation root stack", "navigation", "info");

  try {
    await NativeNavigation.present({
      component: {
        type: "stack",
        components: [
          {
            type: "view",
            path: "/",
            stackItem: defaultStackItemConfig,
          },
        ],
      },
      animated: false,
    });

    addBreadcrumb("Native navigation root stack setup completed", "navigation", "info");
  } catch (error) {
    addBreadcrumb("Failed to setup native navigation root stack", "navigation", "error");
    throw error;
  }
}

// Track if a modal is currently being presented to prevent duplicates
let isPresenting = false;

async function presentSignInModal() {
  // Prevent multiple presentations
  if (isPresenting) {
    addBreadcrumb("Sign in modal presentation skipped - already presenting", "navigation", "warning");
    console.log("Sign in modal already being presented");
    return Promise.resolve();
  }

  addBreadcrumb("Presenting sign in modal", "navigation", "info");
  isPresenting = true;

  try {
    const result = await NativeNavigation.present({
      component: {
        type: "view",
        path: "/sign-in-modal",
      },
      style: "formSheet",
      animated: true,
      cancellable: false,
    });

    addBreadcrumb("Sign in modal presented successfully", "navigation", "info");
    return result;
  } catch (error) {
    addBreadcrumb("Failed to present sign in modal", "navigation", "error");
    throw error;
  } finally {
    isPresenting = false;
  }
}

export function AppRouter() {
  const token = useAuthToken();
  const isAuthenticated = !!token;

  // Track authentication state changes
  useEffect(() => {
    if (isAuthenticated) {
      addBreadcrumb("User authenticated", "auth", "info");
    } else {
      addBreadcrumb("User not authenticated", "auth", "info");
    }
  }, [isAuthenticated]);

  useEffect(() => {
    addBreadcrumb("AppRouter component mounted", "navigation", "info");

    if (isNativeNavigationAvailable()) {
      setupRootStack().catch((error) => {
        addBreadcrumb("Root stack setup failed", "navigation", "error");
        console.error("Failed to setup root stack:", error);
      });
    }
  }, []);

  useEffect(() => {
    if (!isAuthenticated) {
      addBreadcrumb("Scheduling sign-in modal presentation", "auth", "info");

      // Small delay to ensure UI is ready
      const timer = setTimeout(() => {
        presentSignInModal().catch((error) => {
          addBreadcrumb("Sign-in modal presentation failed", "auth", "error");
          console.error("Failed to present sign-in modal:", error);
        });
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [token, isAuthenticated]);

  const Router = isNativeNavigationAvailable()
    ? NativeNavigationRouter
    : BrowserRouter;
  const routerProps = isNativeNavigationAvailable()
    ? { navigation: nativeNavigationNavigatorOptions }
    : {};

  return (
    <NativeNavigationProvider value={nativeNavigationReact}>
      <Router {...routerProps}>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/dream/:id" element={<DreamDetailPage />} />
          <Route path="/sign-in-modal" element={<SignInModalContainer />} />
        </Routes>
      </Router>
    </NativeNavigationProvider>
  );
}
