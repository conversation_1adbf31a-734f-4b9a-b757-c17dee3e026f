import { NativeNavigation, StackItemSpec, isNativeNavigationAvailable } from 'capacitor-native-navigation';

export const defaultStackItemConfig: StackItemSpec = {
  bar: {
    background: { color: "#14082C" },
    title: { color: "#C7B2FF" },
    buttons: { color: "#C7B2FF" },
  },
};

export async function goToDream(
  dreamId: string,
  navigate: (path: string, options: { replace: boolean }) => void,
  options: { replace?: boolean } = {},
) {
  const path = `/dream/${dreamId}`;

  if (isNativeNavigationAvailable()) {
    await NativeNavigation.push({
      component: {
        type: 'view',
        path,
        stackItem: defaultStackItemConfig,
      },
      mode: options.replace ? 'replace' : undefined,
    });
  } else {
    navigate(path, { replace: options.replace ?? false });
  }
}
