{"name": "capacitor-native-navigation-root", "private": true, "version": "0.0.0", "description": "", "scripts": {"build": "pnpm --recursive build", "check-updates": "pnpm dlx npm-check-updates -u && pnpm --recursive exec pnpm dlx npm-check-updates -u", "clean": "pnpm --recursive clean", "clean:node": "pnpm --recursive exec rm -rf node_modules && rm -rf node_modules", "link": "pnpm --recursive exec pnpm link --global", "release": "pnpm clean && pnpm build && pnpm test && pnpm exec changeset publish && pnpm install", "release:version": "pnpm changeset version && pnpm install", "match-deps": "./scripts/match-deps.sh", "test": "pnpm --recursive test", "watch": "(pnpm --recursive build || true) && pnpm --parallel watch"}, "repository": {"type": "git", "url": "git+https://github.com/cactuslab/capacitor-native-navigation.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/cactuslab/capacitor-native-navigation/issues"}, "homepage": "https://github.com/cactuslab/capacitor-native-navigation#readme", "devDependencies": {"@changesets/cli": "^2.29.2", "@types/node": "~22", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "eslint": "^9.25.1", "eslint-config-react-app": "^7.0.1", "rimraf": "^6.0.1", "rollup": "^4.40.0", "typescript": "~5.8.3"}}