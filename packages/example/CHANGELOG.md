# capacitor-app

## 2.5.2

### Patch Changes

- Updated dependencies [68d43f2]
  - capacitor-native-navigation@0.10.2
  - capacitor-native-navigation-react@6.3.4
  - capacitor-native-navigation-react-router@7.4.3

## 2.5.1

### Patch Changes

- Updated dependencies [324bfee]
  - capacitor-native-navigation@0.10.1
  - capacitor-native-navigation-react@6.3.3
  - capacitor-native-navigation-react-router@7.4.2

## 2.5.0

### Minor Changes

- 5fe2132: Improved android toolbar behaviour

### Patch Changes

- 4c3e387: ios: Carry capacitor configurations into the Native Navigation webview
- 51710b7: iOS: Apply button color to the Back button arrow
- 15aec09: android: fixed jvm and kotlin toolchain inconsistency
- Updated dependencies [4c3e387]
- Updated dependencies [5fe2132]
- Updated dependencies [51710b7]
- Updated dependencies [d5d87b8]
- Updated dependencies [15aec09]
  - capacitor-native-navigation@0.10.0
  - capacitor-native-navigation-react@6.3.2
  - capacitor-native-navigation-react-router@7.4.1

## 2.4.2

### Patch Changes

- 689da2c: Fix plugin Podspec name
- Updated dependencies [b667720]
- Updated dependencies [689da2c]
- Updated dependencies [8754303]
- Updated dependencies [fd8354d]
- Updated dependencies [7dffc65]
  - capacitor-native-navigation-react-router@7.4.0
  - capacitor-native-navigation@0.9.1
  - capacitor-native-navigation-react@6.3.1

## 2.4.1

### Patch Changes

- 3316211: Upgrade dependencies
- Updated dependencies [4d4c694]
- Updated dependencies [b16c1c9]
- Updated dependencies [ea0aa14]
- Updated dependencies [3316211]
  - capacitor-native-navigation-react-router@7.3.0
  - capacitor-native-navigation@0.9.0
  - capacitor-native-navigation-react@6.3.0

## 2.4.0

### Minor Changes

- 79e38e8: Rename packages from `@cactuslab` to no scope

### Patch Changes

- 6961cb4: Update Capacitor dependencies
- Updated dependencies [79e38e8]
  - capacitor-native-navigation-react-router@7.2.0
  - capacitor-native-navigation@0.8.0
  - capacitor-native-navigation-react@6.2.0

## 2.3.0

### Minor Changes

- 42ec557: Add state to Stack and Tab specs, which get combined with View state
- 85ac89c: Simplified and standardised leftItems behaviour.

### Patch Changes

- 07e361a: Android: added support for hardware back button interruption
- Updated dependencies [5385d2d]
- Updated dependencies [07e361a]
- Updated dependencies [42ec557]
- Updated dependencies [5d72bb2]
- Updated dependencies [bf30927]
- Updated dependencies [85ac89c]
- Updated dependencies [2b2f1f7]
- Updated dependencies [660661b]
  - capacitor-native-navigation@0.7.0
  - capacitor-native-navigation-react-router@7.1.0
  - capacitor-native-navigation-react@6.1.0

## 2.3.0-next.0

### Minor Changes

- 42ec557: Add state to Stack and Tab specs, which get combined with View state
- 85ac89c: Simplified and standardised leftItems behaviour.

### Patch Changes

- 07e361a: Android: added support for hardware back button interruption
- Updated dependencies [5385d2d]
- Updated dependencies [07e361a]
- Updated dependencies [42ec557]
- Updated dependencies [5d72bb2]
- Updated dependencies [bf30927]
- Updated dependencies [85ac89c]
- Updated dependencies [660661b]
  - capacitor-native-navigation@0.7.0-next.0
  - capacitor-native-navigation-react-router@7.1.0-next.0
  - capacitor-native-navigation-react@6.1.0-next.0

## 2.2.2

### Patch Changes

- Updated dependencies [eab1bec]
  - capacitor-native-navigation-react-router@7.0.0

## 2.2.1

### Patch Changes

- Updated dependencies [aa9599f]
- Updated dependencies [63e89de]
- Updated dependencies [ec8aadd]
- Updated dependencies [d88b6ce]
- Updated dependencies [c15bb76]
- Updated dependencies [65b9585]
- Updated dependencies [b73eacb]
  - capacitor-native-navigation@0.6.0
  - capacitor-native-navigation-react@6.0.0
  - capacitor-native-navigation-react-router@6.0.0

## 2.2.0

### Minor Changes

- 3a92a06: iOS: ensure roots are presented in the correct order
- 90a909b: Add ViewUpdate as an optional parameter to `useNativeNavigationViewContext`

### Patch Changes

- Updated dependencies [86e67e8]
- Updated dependencies [9842b3f]
- Updated dependencies [3a92a06]
- Updated dependencies [90a909b]
- Updated dependencies [10fe5f1]
- Updated dependencies [991eceb]
- Updated dependencies [718859d]
  - capacitor-native-navigation@0.5.0
  - capacitor-native-navigation-react@5.0.0
  - capacitor-native-navigation-react-router@5.0.0

## 2.1.0

### Minor Changes

- 4d7405b: Add open and onClose to NativeNavigationModal

### Patch Changes

- Updated dependencies [f2c453a]
- Updated dependencies [136b67d]
- Updated dependencies [4d7405b]
- Updated dependencies [8840a5b]
- Updated dependencies [06766a5]
- Updated dependencies [58c59fa]
- Updated dependencies [7ea0af6]
- Updated dependencies [0af59f1]
- Updated dependencies [209789b]
- Updated dependencies [dfe3463]
- Updated dependencies [a18842c]
  - capacitor-native-navigation@0.4.1
  - capacitor-native-navigation-react@4.1.0
  - capacitor-native-navigation-react-router@4.0.1

## 2.0.0

### Major Changes

- 4d741e8: Move NativeNavigationViews from native-navigation-react to native-navigation-react-router as NativeNavigationRouter
- ea64a47: Change to using React portals from roots.

  This is in order to be able to wrap contexts and providers around the whole application, as you would
  usually do in a React application using routing.

### Minor Changes

- ed67a32: Upgrade to Capacitor 5 and update other dependencies

### Patch Changes

- Updated dependencies [2862c55]
- Updated dependencies [f745451]
- Updated dependencies [bb4fc40]
- Updated dependencies [d17babb]
- Updated dependencies [b6b815d]
- Updated dependencies [bc843cb]
- Updated dependencies [cd866e4]
- Updated dependencies [51cec3b]
- Updated dependencies [7bef20b]
- Updated dependencies [6c144ab]
- Updated dependencies [64a3844]
- Updated dependencies [6a76ab3]
- Updated dependencies [13a6e92]
- Updated dependencies [52b7329]
- Updated dependencies [ec75c6c]
- Updated dependencies [905e941]
- Updated dependencies [2d8d41d]
- Updated dependencies [4d741e8]
- Updated dependencies [ad8cd03]
- Updated dependencies [72d857c]
- Updated dependencies [d0261dd]
- Updated dependencies [02b0af8]
- Updated dependencies [35f49ff]
- Updated dependencies [ea64a47]
- Updated dependencies [2add2a5]
- Updated dependencies [8cbf96b]
- Updated dependencies [ed67a32]
- Updated dependencies [a83dd7e]
  - capacitor-native-navigation-react-router@4.0.0
  - capacitor-native-navigation@0.4.0
  - capacitor-native-navigation-react@4.0.0

## 1.2.1

### Patch Changes

- 815da46: Upgrade dependencies
- Updated dependencies [3f25211]
- Updated dependencies [e2706c1]
- Updated dependencies [815da46]
- Updated dependencies [8eb7b84]
- Updated dependencies [f6b3925]
  - capacitor-native-navigation@0.3.0
  - capacitor-native-navigation-react-router@3.0.0
  - capacitor-native-navigation-react@3.0.0

## 1.2.0

### Minor Changes

- 07a0376: feature: Added support for disabling system back action on stack

### Patch Changes

- c7971af: Removed need for patching capacitor
- Updated dependencies [07a0376]
- Updated dependencies [e6ef6ea]
- Updated dependencies [1c09146]
- Updated dependencies [c7971af]
  - capacitor-native-navigation@0.2.0
  - capacitor-native-navigation-react@2.0.0
  - capacitor-native-navigation-react-router@2.0.0

## 1.1.0

### Minor Changes

- c901c24: Modals: Allow option to prevent system gestures for dismissing
- a0a7df3: Modal navigation support

### Patch Changes

- Updated dependencies [c901c24]
- Updated dependencies [a0a7df3]
- Updated dependencies [cf84e19]
  - capacitor-native-navigation@0.1.0
  - capacitor-native-navigation-react@1.0.0
  - capacitor-native-navigation-react-router@1.0.0

## 1.0.8

### Patch Changes

- Updated dependencies [65565e2]
- Updated dependencies [740123c]
- Updated dependencies [da2dc51]
  - capacitor-native-navigation@0.0.8
  - capacitor-native-navigation-react@0.0.9
  - capacitor-native-navigation-react-router@0.0.8

## 1.0.7

### Patch Changes

- da0bb70: android: support for handling of external links
- 304ab7a: android: Added support for transparent title bars. Introduces new variable --native-navigation-inset-top to allow the application to inject insets.
- Updated dependencies [1056118]
- Updated dependencies [8a817cd]
- Updated dependencies [fa55c7a]
- Updated dependencies [da0bb70]
- Updated dependencies [fade427]
- Updated dependencies [edc92bf]
- Updated dependencies [477dbd8]
- Updated dependencies [4f61d1c]
- Updated dependencies [1b2463c]
- Updated dependencies [304ab7a]
- Updated dependencies [2cef744]
- Updated dependencies [5959ada]
- Updated dependencies [718edfe]
- Updated dependencies [99b56d7]
- Updated dependencies [35fd1ce]
  - capacitor-native-navigation@0.0.7
  - capacitor-native-navigation-react@0.0.8
  - capacitor-native-navigation-react-router@0.0.7

## 1.0.6

### Patch Changes

- Updated dependencies [d45530c]
- Updated dependencies [e1abe83]
  - capacitor-native-navigation@0.0.6
  - capacitor-native-navigation-react@0.0.7
  - capacitor-native-navigation-react-router@0.0.6

## 1.0.5

### Patch Changes

- Updated dependencies [51ca1de]
- Updated dependencies [fb4fec9]
  - capacitor-native-navigation@0.0.5
  - capacitor-native-navigation-react-router@0.0.5
  - capacitor-native-navigation-react@0.0.6

## 1.0.4

### Patch Changes

- Updated dependencies [f8ef128]
- Updated dependencies [b11eb70]
- Updated dependencies [e66a5a7]
- Updated dependencies [33377b1]
- Updated dependencies [258b8cc]
  - capacitor-native-navigation@0.0.4
  - capacitor-native-navigation-react@0.0.5
  - capacitor-native-navigation-react-router@0.0.4

## 1.0.3

### Patch Changes

- 08188df: Replace `setRoot` with `present` as they do basically equivalent things
- a035895: android: Added stable support for pushing and popping on stacks with title options.
- 520c98a: Native React context no longer throws errors on web platform
- Updated dependencies [7148148]
- Updated dependencies [9b285f1]
- Updated dependencies [08188df]
- Updated dependencies [a035895]
- Updated dependencies [7395386]
- Updated dependencies [06493e9]
- Updated dependencies [6933f34]
- Updated dependencies [2e34b18]
- Updated dependencies [520c98a]
- Updated dependencies [5654446]
- Updated dependencies [00c33e8]
- Updated dependencies [5c463f7]
- Updated dependencies [b6f65c6]
- Updated dependencies [b877369]
- Updated dependencies [ff0779e]
- Updated dependencies [bb9dbd9]
- Updated dependencies [fccd7be]
- Updated dependencies [0851d48]
- Updated dependencies [881a70e]
- Updated dependencies [be55f83]
- Updated dependencies [e739c20]
- Updated dependencies [6981173]
- Updated dependencies [364955d]
- Updated dependencies [92bfc86]
- Updated dependencies [a4b2f23]
- Updated dependencies [324870c]
  - capacitor-native-navigation@0.0.3
  - capacitor-native-navigation-react@0.0.4
  - capacitor-native-navigation-react-router@0.0.3

## 1.0.2

### Patch Changes

- Updated dependencies [c5bd5ba]
  - capacitor-native-navigation-react@0.0.3

## 1.0.1

### Patch Changes

- Updated dependencies [d8075be]
  - capacitor-native-navigation@0.0.2
  - capacitor-native-navigation-react@0.0.2
  - capacitor-native-navigation-react-router@0.0.2
