{"name": "capacitor-app", "version": "2.5.2", "description": "An Amazing Capacitor App", "main": "index.js", "type": "module", "private": true, "keywords": ["capacitor", "mobile"], "scripts": {"cap:local": "CAP_SERVER=http://$(ipconfig getifaddr en0 || ipconfig getifaddr en1):5173/ cap sync", "start": "vite", "start:host": "vite --host $(ipconfig getifaddr en0 || ipconfig getifaddr en1)", "build": "vite build", "preview": "vite preview"}, "dependencies": {"capacitor-native-navigation": "workspace:*", "capacitor-native-navigation-react": "workspace:*", "capacitor-native-navigation-react-router": "workspace:*", "@capacitor/android": "^7.2.0", "@capacitor/camera": "^7.0.1", "@capacitor/core": "^7.2.0", "@capacitor/ios": "^7.2.0", "@capacitor/splash-screen": "latest", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.0"}, "devDependencies": {"@capacitor/cli": "^7.2.0", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.4.1", "vite": "^6.3.3"}, "author": "", "license": "ISC"}