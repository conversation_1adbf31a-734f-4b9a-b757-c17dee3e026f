{"name": "capacitor-native-navigation-history", "version": "6.3.4", "description": "Navigation support using History for Native navigation for Capacitor apps", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "files": ["dist/"], "exports": {".": "./dist/esm/index.js"}, "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/cactuslab/capacitor-native-navigation.git"}, "bugs": {"url": "https://github.com/cactuslab/capacitor-native-navigation/issues"}, "keywords": ["capacitor", "plugin", "native"], "scripts": {"build": "tsc && rollup -c rollup.config.mjs", "clean": "rimraf ./dist", "watch": "tsc --watch", "prepublishOnly": "pnpm run build"}, "dependencies": {"capacitor-native-navigation": "workspace:*"}, "devDependencies": {"capacitor-native-navigation": "workspace:*", "@capacitor/core": "^7.2.0", "@types/history": "^4.7.11"}, "peerDependencies": {"@capacitor/core": ">=5", "react": "~18", "react-dom": "~18"}, "capacitor": {"ios": {"src": "ios"}, "android": {"src": "android"}}}