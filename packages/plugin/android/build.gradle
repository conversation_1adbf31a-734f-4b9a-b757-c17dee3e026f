
buildscript {
    ext {
        junitVersion = project.hasProperty('junitVersion') ? rootProject.ext.junitVersion : '4.13.2'
        androidxAppCompatVersion = project.hasProperty('androidxAppCompatVersion') ? rootProject.ext.androidxAppCompatVersion : '1.7.0'
        androidxJunitVersion = project.hasProperty('androidxJunitVersion') ? rootProject.ext.androidxJunitVersion : '1.1.3'
        androidxEspressoCoreVersion = project.hasProperty('androidxEspressoCoreVersion') ? rootProject.ext.androidxEspressoCoreVersion : '3.4.0'
        kotlinVersion = project.hasProperty('kotlinVersion') ? rootProject.ext.kotlinVersion : '2.0.20'
        kotlinCore = project.hasProperty('kotlinCore') ? rootProject.ext.kotlinCore : '1.13.1'
        coroutines = project.hasProperty('coroutines') ? rootProject.ext.coroutines : '1.8.1'
        navigationVersion = project.hasProperty('navigationVersion') ? rootProject.ext.navigationVersion : '2.8.3'
        androidxLifecyleVersion = project.hasProperty('androidxLifecycleVersion') ? rootProject.ext.androidxLifecycleVersion : '2.8.6'
        coilVersion = project.hasProperty('coilVersion') ? rootProject.ext.coilVersion : '2.2.2'
    }

    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.7.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"
        classpath "androidx.navigation:navigation-safe-args-gradle-plugin:$navigationVersion"
    }
}

apply plugin: 'com.android.library'
apply plugin: 'org.jetbrains.kotlin.android'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'androidx.navigation.safeargs.kotlin'

android {
    namespace "com.cactuslab.capacitor.nativenavigation"
    compileSdkVersion project.hasProperty('compileSdkVersion') ? rootProject.ext.compileSdkVersion : 32
    defaultConfig {
        minSdkVersion project.hasProperty('minSdkVersion') ? rootProject.ext.minSdkVersion : 22
        targetSdkVersion project.hasProperty('targetSdkVersion') ? rootProject.ext.targetSdkVersion : 32
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    lintOptions {
        abortOnError false
    }
    buildFeatures {
        viewBinding true
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlin {
        jvmToolchain(17)
    }
}

repositories {
    google()
    mavenCentral()
}


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(':capacitor-android')
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlinVersion"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$coroutines"
    implementation "androidx.appcompat:appcompat:$androidxAppCompatVersion"
    implementation "androidx.core:core-ktx:$kotlinCore"
    implementation "androidx.activity:activity-ktx:$androidxActivityVersion"
    implementation "androidx.fragment:fragment-ktx:$androidxFragmentVersion"
    implementation "androidx.navigation:navigation-runtime-ktx:$navigationVersion"
    implementation "androidx.navigation:navigation-fragment-ktx:$navigationVersion"
    implementation "androidx.navigation:navigation-ui-ktx:$navigationVersion"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$androidxLifecyleVersion"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$androidxLifecyleVersion"
    implementation 'org.jsoup:jsoup:1.15.3'
    implementation "androidx.webkit:webkit:$androidxWebkitVersion"
    implementation "io.coil-kt:coil:$coilVersion"

    testImplementation "junit:junit:$junitVersion"
    androidTestImplementation "androidx.test.ext:junit:$androidxJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$androidxEspressoCoreVersion"
}
