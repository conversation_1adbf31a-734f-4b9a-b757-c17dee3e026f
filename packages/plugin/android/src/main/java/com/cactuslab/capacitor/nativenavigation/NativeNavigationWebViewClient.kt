package com.cactuslab.capacitor.nativenavigation

import android.net.Uri
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import com.getcapacitor.Bridge

class NativeNavigationWebViewClient(val bridge: Bridge): WebViewClient() {

    override fun shouldInterceptRequest(
        view: WebView?,
        request: WebResourceRequest?
    ): WebResourceResponse? {
        return bridge.webViewClient.shouldInterceptRequest(view, request)
    }

    override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): <PERSON><PERSON><PERSON> {
        return bridge.webViewClient.shouldOverrideUrlLoading(view, request)
    }

    @Deprecated("Deprecated in Java")
    override fun shouldOverrideUrlLoading(view: WebView?, url: String?): <PERSON><PERSON><PERSON> {
        return bridge.webViewClient.shouldOverrideUrlLoading(view, url)
    }

}