<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/design_default_color_background"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        android:elevation="0dp"
        android:stateListAnimator="@animator/no_elevation_state_list_animator"
        app:liftOnScroll="false"
        android:id="@+id/app_bar_layout"
        >

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?android:attr/actionBarSize"
            app:layout_scrollFlags="enterAlways|snap"
            tools:title="home" />

    </com.google.android.material.appbar.AppBarLayout>


</androidx.coordinatorlayout.widget.CoordinatorLayout>