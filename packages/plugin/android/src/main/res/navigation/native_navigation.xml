<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/native_navigation"
    app:startDestination="@id/navigation_view_fragment">

    <fragment
        android:id="@+id/navigation_view_fragment"
        android:label="Home"
        android:name="com.cactuslab.capacitor.nativenavigation.ui.ViewSpecFragment"
        tools:layout="@layout/fragment_screen"
        >

        <argument
            android:name="optionsId"
            app:argType="string"
            app:nullable="true" />

    </fragment>

    <action
        android:id="@+id/action_global_nav_screen"
        app:destination="@id/navigation_view_fragment"
        app:enterAnim="@anim/slide_in_right"
        app:exitAnim="@anim/slide_out_left"
        app:popEnterAnim="@anim/slide_in_left"
        app:popExitAnim="@anim/slide_out_right"
        />

</navigation>