{"name": "capacitor-native-navigation", "version": "0.10.2", "description": "Native navigation for Capacitor apps", "type": "module", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "files": ["android/src/main/", "android/build.gradle", "dist/", "ios/Plugin/", "CapacitorNativeNavigation.podspec"], "exports": {".": "./dist/esm/index.js", "./package.json": "./package.json"}, "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/cactuslab/capacitor-native-navigation.git"}, "bugs": {"url": "https://github.com/cactuslab/capacitor-native-navigation/issues"}, "keywords": ["capacitor", "plugin", "native"], "scripts": {"verify": "pnpm run verify:ios && pnpm run verify:android && pnpm run verify:web", "verify:ios": "cd ios && bundle exec pod install && xcodebuild -workspace Plugin.xcworkspace -scheme Plugin -destination generic/platform=iOS && cd ..", "verify:android": "cd android && ./gradlew clean build test && cd ..", "verify:web": "pnpm run build", "fmt": "pnpm run eslint -- --fix && pnpm run prettier -- --write && pnpm run swiftlint -- --fix --format", "prettier": "prettier \"**/*.{css,html,ts,js,java}\"", "swiftlint": "node-swiftlint", "docgen": "docgen --api NativeNavigationPlugin --output-readme README.md --output-json dist/docs.json", "build": "pnpm run docgen && tsc && rollup -c rollup.config.mjs", "clean": "rimraf ./dist", "watch": "tsc --watch", "prepublishOnly": "pnpm run build"}, "devDependencies": {"@capacitor/android": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/docgen": "^0.3.0", "@capacitor/ios": "^7.2.0", "@ionic/prettier-config": "^4.0.0", "@ionic/swiftlint-config": "^2.0.0", "prettier": "~3.5.3", "prettier-plugin-java": "~2.6.7", "swiftlint": "^2.0.0"}, "peerDependencies": {"@capacitor/core": ">=5"}, "prettier": "@ionic/prettier-config", "swiftlint": "@ionic/swiftlint-config", "capacitor": {"ios": {"src": "ios"}, "android": {"src": "android"}}}