{"name": "capacitor-native-navigation-react-router", "version": "7.4.3", "description": "React Router support for Native navigation for Capacitor apps", "type": "module", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "files": ["dist/"], "exports": {".": "./dist/esm/index.js"}, "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/cactuslab/capacitor-native-navigation.git"}, "bugs": {"url": "https://github.com/cactuslab/capacitor-native-navigation/issues"}, "keywords": ["capacitor", "plugin", "native"], "scripts": {"build": "tsc && rollup -c rollup.config.mjs", "clean": "rimraf ./dist", "watch": "tsc --watch", "prepublishOnly": "pnpm run build"}, "dependencies": {"capacitor-native-navigation": "workspace:*", "capacitor-native-navigation-react": "workspace:*", "path-to-regexp": "^8.2.0"}, "devDependencies": {"@capacitor/core": "^7.2.0", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "capacitor-native-navigation": "workspace:*", "react-router-dom": "^6.30.0"}, "peerDependencies": {"@capacitor/core": ">=5", "react": "^18", "react-dom": "^18", "react-router": "^6", "react-router-dom": "^6"}}