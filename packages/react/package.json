{"name": "capacitor-native-navigation-react", "version": "6.3.4", "description": "React support for Native navigation for Capacitor apps", "type": "module", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "files": ["dist/"], "exports": {".": "./dist/esm/index.js", "./context": "./dist/esm/context.js"}, "typesVersions": {"*": {"context": ["./dist/esm/context.d.ts"]}}, "author": "", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/cactuslab/capacitor-native-navigation.git"}, "bugs": {"url": "https://github.com/cactuslab/capacitor-native-navigation/issues"}, "keywords": ["capacitor", "plugin", "native"], "scripts": {"prettier": "prettier \"**/*.{css,html,ts,js,java}\"", "build": "tsc && rollup -c rollup.config.mjs", "clean": "rimraf ./dist", "watch": "tsc --watch", "prepublishOnly": "pnpm run build"}, "dependencies": {"capacitor-native-navigation": "workspace:*", "fast-deep-equal": "^3.1.3"}, "devDependencies": {"@capacitor/core": "^7.2.0", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "capacitor-native-navigation": "workspace:*"}, "peerDependencies": {"@capacitor/core": ">=5", "react": "^18", "react-dom": "^18"}}